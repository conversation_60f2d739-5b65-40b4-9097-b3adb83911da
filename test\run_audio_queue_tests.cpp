/**
 * @file run_audio_queue_tests.cpp
 * @brief Simple test runner for audio queue functionality
 *
 * This file provides a minimal test environment to verify the audio queue
 * system works correctly without requiring the full ESP32 environment.
 */

#include <iostream>
#include <queue>
#include <mutex>
#include <string>
#include <cassert>
#include <thread>

// Mock Arduino String class
class String
{
public:
    std::string data;

    String() = default;
    String(const char *str) : data(str) {}
    String(const std::string &str) : data(str) {}

    const char *c_str() const { return data.c_str(); }
    size_t length() const { return data.length(); }

    bool operator==(const String &other) const { return data == other.data; }
    bool operator!=(const String &other) const { return data != other.data; }
};

// Global variables (simulating main.cpp)
std::mutex audio_mutex;
std::queue<String> audio_queue;
unsigned long last_audio_trigger = 0;
const unsigned long DEBOUNCE_DELAY = 300;

// Mock variables
bool mock_voice_state = true;
int mock_page_level = 1;
unsigned long mock_millis_value = 0;

// Mock functions
int info_get_Voice_State()
{
    return mock_voice_state ? 1 : 0;
}

struct MockPage
{
    int page_level;
};

MockPage mock_page = {1};

MockPage *get_cur_page()
{
    mock_page.page_level = mock_page_level;
    return &mock_page;
}

unsigned long millis()
{
    return mock_millis_value;
}

// Audio queue management functions (from main.cpp)
size_t audio_queue_size()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    return audio_queue.size();
}

void audio_queue_clear()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    while (!audio_queue.empty())
    {
        audio_queue.pop();
    }
    std::cout << "Audio queue cleared" << std::endl;
}

bool audio_queue_is_empty()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    return audio_queue.empty();
}

// Audio prompt function (from main.cpp)
void audio_prompt(const char *filename)
{
    std::cout << "audio_prompt: " << filename << std::endl;
    if (info_get_Voice_State() == 1 || get_cur_page()->page_level <= 2)
    {
        std::lock_guard<std::mutex> lock(audio_mutex);
        unsigned long current_time = millis();
        if (current_time - last_audio_trigger > DEBOUNCE_DELAY)
        {
            last_audio_trigger = current_time;

            // Add to queue instead of playing directly
            audio_queue.push(String(filename));
            std::cout << "Added to audio queue: " << filename
                      << " (queue size: " << audio_queue.size() << ")" << std::endl;
        }
    }
}

// Test functions
void test_basic_operations()
{
    std::cout << "\n=== Testing Basic Operations ===" << std::endl;

    audio_queue_clear();
    assert(audio_queue_is_empty());
    assert(audio_queue_size() == 0);
    std::cout << "✓ Initial state correct" << std::endl;

    mock_millis_value = 1000;
    audio_prompt("test1");
    assert(!audio_queue_is_empty());
    assert(audio_queue_size() == 1);
    std::cout << "✓ Added first item" << std::endl;

    mock_millis_value = 2000;
    audio_prompt("test2");
    assert(audio_queue_size() == 2);
    std::cout << "✓ Added second item" << std::endl;

    audio_queue_clear();
    assert(audio_queue_is_empty());
    assert(audio_queue_size() == 0);
    std::cout << "✓ Queue cleared successfully" << std::endl;
}

void test_debounce()
{
    std::cout << "\n=== Testing Debounce Functionality ===" << std::endl;

    audio_queue_clear();

    mock_millis_value = 1000;
    audio_prompt("test1");
    assert(audio_queue_size() == 1);
    std::cout << "✓ First call added to queue" << std::endl;

    mock_millis_value = 1200; // Within debounce period
    audio_prompt("test2");
    assert(audio_queue_size() == 1);
    std::cout << "✓ Second call within debounce ignored" << std::endl;

    mock_millis_value = 1400; // After debounce period
    audio_prompt("test3");
    assert(audio_queue_size() == 2);
    std::cout << "✓ Call after debounce added" << std::endl;
}

void test_voice_conditions()
{
    std::cout << "\n=== Testing Voice State Conditions ===" << std::endl;

    audio_queue_clear();
    mock_millis_value = 1000;

    // Voice disabled, high page level
    mock_voice_state = false;
    mock_page_level = 5;
    audio_prompt("test1");
    assert(audio_queue_size() == 0);
    std::cout << "✓ Voice disabled + high page level = no add" << std::endl;

    // Voice enabled, high page level
    mock_voice_state = true;
    mock_page_level = 5;
    mock_millis_value = 2000;
    audio_prompt("test2");
    assert(audio_queue_size() == 0);
    std::cout << "✓ Voice enabled + high page level = no add" << std::endl;

    // Voice disabled, low page level
    mock_voice_state = false;
    mock_page_level = 1;
    mock_millis_value = 3000;
    audio_prompt("test3");
    assert(audio_queue_size() == 1);
    std::cout << "✓ Voice disabled + low page level = add" << std::endl;

    // Voice enabled, low page level
    audio_queue_clear();
    mock_voice_state = true;
    mock_page_level = 2;
    mock_millis_value = 4000;
    audio_prompt("test4");
    assert(audio_queue_size() == 1);
    std::cout << "✓ Voice enabled + low page level = add" << std::endl;
}

void test_multiple_items()
{
    std::cout << "\n=== Testing Multiple Items ===" << std::endl;

    audio_queue_clear();

    const char *test_files[] = {"file1", "file2", "file3", "file4", "file5"};
    const int num_files = sizeof(test_files) / sizeof(test_files[0]);

    for (int i = 0; i < num_files; i++)
    {
        mock_millis_value = 1000 + (i * 500);
        audio_prompt(test_files[i]);
    }

    assert(audio_queue_size() == num_files);
    std::cout << "✓ Added " << num_files << " items to queue" << std::endl;

    audio_queue_clear();
    assert(audio_queue_size() == 0);
    std::cout << "✓ Cleared all items" << std::endl;
}

int main()
{
    std::cout << "Starting Audio Queue Tests..." << std::endl;

    try
    {
        test_basic_operations();
        test_debounce();
        test_voice_conditions();
        test_multiple_items();

        std::cout << "\n🎉 All tests passed!" << std::endl;
        return 0;
    }
    catch (const std::exception &e)
    {
        std::cout << "\n❌ Test failed: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cout << "\n❌ Test failed with unknown error" << std::endl;
        return 1;
    }
}
