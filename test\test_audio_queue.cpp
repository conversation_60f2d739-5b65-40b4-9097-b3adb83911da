#include <unity.h>
#include <Arduino.h>
#include <queue>
#include <mutex>
#include <string>

// Mock the external dependencies
extern std::mutex audio_mutex;
extern std::queue<String> audio_queue;
extern unsigned long last_audio_trigger;
extern const unsigned long DEBOUNCE_DELAY;

// Declare the functions we want to test
extern size_t audio_queue_size();
extern void audio_queue_clear();
extern bool audio_queue_is_empty();
extern void audio_prompt(const char *filename);

// Mock functions for testing
bool mock_voice_state = true;
int mock_page_level = 1;
unsigned long mock_millis_value = 0;

// Mock implementations
int info_get_Voice_State() {
    return mock_voice_state ? 1 : 0;
}

struct MockPage {
    int page_level;
};

MockPage mock_page = {1};

MockPage* get_cur_page() {
    mock_page.page_level = mock_page_level;
    return &mock_page;
}

unsigned long millis() {
    return mock_millis_value;
}

void setUp(void) {
    // Clear queue before each test
    audio_queue_clear();
    last_audio_trigger = 0;
    mock_millis_value = 0;
    mock_voice_state = true;
    mock_page_level = 1;
}

void tearDown(void) {
    // Clean up after each test
    audio_queue_clear();
}

void test_audio_queue_basic_operations(void) {
    // Test initial state
    TEST_ASSERT_TRUE_MESSAGE(audio_queue_is_empty(), "Queue should be empty initially");
    TEST_ASSERT_EQUAL_MESSAGE(0, audio_queue_size(), "Queue size should be 0 initially");
    
    // Add items to queue via audio_prompt
    mock_millis_value = 1000;
    audio_prompt("test1");
    
    TEST_ASSERT_FALSE_MESSAGE(audio_queue_is_empty(), "Queue should not be empty after adding item");
    TEST_ASSERT_EQUAL_MESSAGE(1, audio_queue_size(), "Queue size should be 1");
    
    // Add another item
    mock_millis_value = 2000;
    audio_prompt("test2");
    
    TEST_ASSERT_EQUAL_MESSAGE(2, audio_queue_size(), "Queue size should be 2");
    
    // Clear queue
    audio_queue_clear();
    TEST_ASSERT_TRUE_MESSAGE(audio_queue_is_empty(), "Queue should be empty after clear");
    TEST_ASSERT_EQUAL_MESSAGE(0, audio_queue_size(), "Queue size should be 0 after clear");
}

void test_audio_prompt_debounce(void) {
    // Test debounce functionality
    mock_millis_value = 1000;
    audio_prompt("test1");
    TEST_ASSERT_EQUAL_MESSAGE(1, audio_queue_size(), "First call should add to queue");
    
    // Call again within debounce period
    mock_millis_value = 1200; // Only 200ms later, within DEBOUNCE_DELAY (300ms)
    audio_prompt("test2");
    TEST_ASSERT_EQUAL_MESSAGE(1, audio_queue_size(), "Second call within debounce should be ignored");
    
    // Call again after debounce period
    mock_millis_value = 1400; // 400ms from first call, beyond DEBOUNCE_DELAY
    audio_prompt("test3");
    TEST_ASSERT_EQUAL_MESSAGE(2, audio_queue_size(), "Call after debounce should be added");
}

void test_audio_prompt_voice_state_conditions(void) {
    mock_millis_value = 1000;
    
    // Test with voice disabled and high page level
    mock_voice_state = false;
    mock_page_level = 5;
    audio_prompt("test1");
    TEST_ASSERT_EQUAL_MESSAGE(0, audio_queue_size(), "Should not add when voice disabled and page level > 2");
    
    // Test with voice enabled but high page level
    mock_voice_state = true;
    mock_page_level = 5;
    audio_prompt("test2");
    TEST_ASSERT_EQUAL_MESSAGE(0, audio_queue_size(), "Should not add when page level > 2");
    
    // Test with voice disabled but low page level
    mock_voice_state = false;
    mock_page_level = 1;
    audio_prompt("test3");
    TEST_ASSERT_EQUAL_MESSAGE(1, audio_queue_size(), "Should add when page level <= 2 even if voice disabled");
    
    // Clear and test with voice enabled and low page level
    audio_queue_clear();
    mock_voice_state = true;
    mock_page_level = 2;
    mock_millis_value = 2000;
    audio_prompt("test4");
    TEST_ASSERT_EQUAL_MESSAGE(1, audio_queue_size(), "Should add when voice enabled and page level <= 2");
}

void test_audio_queue_thread_safety(void) {
    // This test verifies that the mutex is working correctly
    // We can't easily test actual threading in unit tests, but we can verify
    // that the functions don't crash when called
    
    mock_millis_value = 1000;
    audio_prompt("test1");
    
    // These operations should not interfere with each other
    size_t size1 = audio_queue_size();
    bool empty1 = audio_queue_is_empty();
    
    mock_millis_value = 2000;
    audio_prompt("test2");
    
    size_t size2 = audio_queue_size();
    bool empty2 = audio_queue_is_empty();
    
    TEST_ASSERT_EQUAL_MESSAGE(1, size1, "First size check should be 1");
    TEST_ASSERT_FALSE_MESSAGE(empty1, "First empty check should be false");
    TEST_ASSERT_EQUAL_MESSAGE(2, size2, "Second size check should be 2");
    TEST_ASSERT_FALSE_MESSAGE(empty2, "Second empty check should be false");
}

void test_audio_queue_multiple_items(void) {
    // Test adding multiple items
    const char* test_files[] = {"file1", "file2", "file3", "file4", "file5"};
    const int num_files = sizeof(test_files) / sizeof(test_files[0]);
    
    for (int i = 0; i < num_files; i++) {
        mock_millis_value = 1000 + (i * 500); // Ensure debounce doesn't interfere
        audio_prompt(test_files[i]);
    }
    
    TEST_ASSERT_EQUAL_MESSAGE(num_files, audio_queue_size(), "Should have all files in queue");
    TEST_ASSERT_FALSE_MESSAGE(audio_queue_is_empty(), "Queue should not be empty");
    
    // Clear and verify
    audio_queue_clear();
    TEST_ASSERT_EQUAL_MESSAGE(0, audio_queue_size(), "Queue should be empty after clear");
    TEST_ASSERT_TRUE_MESSAGE(audio_queue_is_empty(), "Queue should be empty after clear");
}

void setup() {
    delay(2000); // Wait for serial monitor
    UNITY_BEGIN();
    
    RUN_TEST(test_audio_queue_basic_operations);
    RUN_TEST(test_audio_prompt_debounce);
    RUN_TEST(test_audio_prompt_voice_state_conditions);
    RUN_TEST(test_audio_queue_thread_safety);
    RUN_TEST(test_audio_queue_multiple_items);
    
    UNITY_END();
}

void loop() {
    // Empty loop for Arduino compatibility
}
