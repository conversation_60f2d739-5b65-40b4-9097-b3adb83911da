#ifndef __MAIN_INCLUDE_H__
#define __MAIN_INCLUDE_H__
#ifdef __cplusplus
extern "C"
{
#endif
#include <Arduino.h>

    // FUNCTION
    // 返回按键相关函数
    void user_exit_press_event_handler(void);
    void user_exit_long_press_event_handler(void);
    // 用户切换按键相关函数
    void user_change_press_event_handler(void);
    void user_change_long_press_event_handler(void);
    // // 弹窗显示函数
    // void pop_disp_message(const char *topic, const char *body, const char *time);
    // 创建数据储存的路径与文件
    void dirAndFileCreate(void);
    // WiFi数据的存储与读取
    bool wifiDataWrite(const char *data);
    bool wifiDataUpdate(const char *data, int index);
    bool wifiDataClean();
    bool wifiDataRead();
    // 消息数据的存储与读取
    bool messageDataWrite(const char *data);
    bool messageDataClean();
    bool messageDataRead();
    // 用户数据的存储与读取
    bool accountDataWrite(const char *data);
    bool accountDataClean();
    bool accountDataRead();
    // ble信任数据的存储与读取
    bool bleInfoDataWrite(const char *data);
    bool bleInfoDataClean();
    bool bleInfoDataRead();
    // 睡眠数据的存储与读取
    bool sleepConfigWrite(const char *data);
    bool sleepConfigClean();
    bool sleepConfigRead();
    // 音频开关的存储与读取
    bool audioConfigWrite(const char *data);
    bool audioConfigClean();
    bool audioConfigRead();
    // 最后一次数据的存储与读取
    bool lastDataWrite(uint8_t cur_index);
    bool lastDataRead();
    bool lastDataClean(void);
    // 离线数据的存储与读取
    bool offlineDataWrite(const char *data);
    void offlineDataRead();
    // 字符串删除函数
    bool deleteString(const char *filename, int start, int end);

    // Voice files are now embedded, no need for network download functions
    // bool get_store_wav_file(void);

    void audio_play(const char *);
    void audio_web_play(const char *);

    // void getFile(const char *file_name);

    void ble_scan_init();
    void bleStopScan();
    void setBleState(uint8_t newState);

    void wifiConfigStart(void);
    void connectWifi(void);

    void display_family_member(void);
    void display_patient_name(uint8_t index);
    void display_message(uint8_t index);

    void messageDelete(uint8_t index);
    void messageDisplayAll();

    int8_t version_compare();
    void ota_begin();

    void wifi_mqtt_topic_create(void);
    void wifi_mqtt_unsub();

    void disp_data_set_with_store(void);
    bool blood_glucose_data_submit(void);
    void reset_pop_display_time(void);

    void wifi_setup_server();
    void wifi_setup_mqtt();

    void ui_log_impl(const char *file, int line, const char *format, ...);

#define ui_log(format, ...) ui_log_impl(__FILE__, __LINE__, format, ##__VA_ARGS__)

#ifdef __cplusplus
}
#endif
#endif // !__MAIN_INCLUDE_H__