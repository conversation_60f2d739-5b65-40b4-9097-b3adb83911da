# 语音播放队列系统

## 问题描述

在原始代码中，当语音系统正在播放一个文件时，如果有新的语音播放请求，会直接失败并显示以下错误：

```
[119221][W][voice_manager.cpp:105] voice_manager_play(): Voice manager is busy playing another file
[119231][W][voice_interface.cpp:103] voice_system_play_prompt(): Failed to play voice prompt 'select_user': Audio system busy
[119243][W][main.cpp:5197] audio_prompt(): Failed to play voice prompt: select_user
```

## 解决方案

实现了一个语音播放队列系统，将语音播放请求排队，而不是直接失败。

### 主要修改

#### 1. 修改 `audio_prompt` 函数

**位置**: `src/main.cpp:5183-5199`

**修改前**:
```cpp
void audio_prompt(const char *filename)
{
    log_i("audio_prompt: %s", filename);
    if (info_get_Voice_State() == 1 || get_cur_page()->page_level <= 2)
    {
        std::lock_guard<std::mutex> lock(audio_mutex);
        unsigned long current_time = millis();
        if (current_time - last_audio_trigger > DEBOUNCE_DELAY)
        {
            last_audio_trigger = current_time;

            // Use new voice system to play directly
            if (!voice_system_play_prompt(filename))
            {
                log_w("Failed to play voice prompt: %s", filename);
            }
        }
    }
}
```

**修改后**:
```cpp
void audio_prompt(const char *filename)
{
    log_i("audio_prompt: %s", filename);
    if (info_get_Voice_State() == 1 || get_cur_page()->page_level <= 2)
    {
        std::lock_guard<std::mutex> lock(audio_mutex);
        unsigned long current_time = millis();
        if (current_time - last_audio_trigger > DEBOUNCE_DELAY)
        {
            last_audio_trigger = current_time;

            // Add to queue instead of playing directly
            audio_queue.push(String(filename));
            log_i("Added to audio queue: %s (queue size: %d)", filename, audio_queue.size());
        }
    }
}
```

#### 2. 修改 `audio_handler` 函数

**位置**: `src/main.cpp:5212-5235`

**添加的队列处理逻辑**:
```cpp
// Process voice system
voice_system_process();

// Process audio queue when not playing
if (!voice_system_is_playing())
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    if (!audio_queue.empty())
    {
        String filename = audio_queue.front();
        audio_queue.pop();
        
        log_i("Processing queued audio: %s (remaining: %d)", filename.c_str(), audio_queue.size());
        
        // Try to play the queued audio
        if (!voice_system_play_prompt(filename.c_str()))
        {
            log_w("Failed to play queued voice prompt: %s", filename.c_str());
        }
    }
}

if (voice_system_is_playing())
    return;
```

#### 3. 添加队列管理函数

**位置**: `src/main.cpp:5185-5204`

```cpp
// Audio queue management functions
size_t audio_queue_size()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    return audio_queue.size();
}

void audio_queue_clear()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    while (!audio_queue.empty())
    {
        audio_queue.pop();
    }
    log_i("Audio queue cleared");
}

bool audio_queue_is_empty()
{
    std::lock_guard<std::mutex> lock(audio_mutex);
    return audio_queue.empty();
}
```

### 工作原理

1. **语音请求排队**: 当 `audio_prompt()` 被调用时，语音文件名被添加到 `audio_queue` 队列中，而不是直接播放。

2. **队列处理**: 在 `audio_handler()` 函数中，当语音系统不在播放状态时，从队列中取出下一个语音文件并播放。

3. **线程安全**: 使用 `std::mutex` 确保队列操作的线程安全。

4. **防抖动**: 保留原有的防抖动机制，防止短时间内重复触发。

### 优势

1. **无语音丢失**: 所有语音播放请求都会被排队，不会因为系统忙碌而丢失。

2. **按顺序播放**: 语音按照请求的顺序依次播放。

3. **线程安全**: 使用互斥锁保护队列操作。

4. **向后兼容**: 不改变现有的API接口，只是内部实现的改进。

## 测试

创建了完整的测试套件来验证队列系统的功能：

- **基本操作测试**: 验证队列的添加、清空、大小检查等基本功能
- **防抖动测试**: 验证防抖动机制正常工作
- **条件测试**: 验证语音状态和页面级别条件判断
- **多项目测试**: 验证多个语音文件的排队和处理
- **队列处理测试**: 验证队列项目的顺序处理

测试文件位置：
- `test/simple_audio_queue_test.cpp` - 简化的测试实现
- `test/test_audio_queue.cpp` - 完整的Unity测试框架实现

## 使用方法

系统的使用方法与之前完全相同，不需要修改任何调用代码：

```cpp
// 语音播放请求会自动排队
audio_prompt("network_success");
audio_prompt("select_user");
audio_prompt("tap_smart_config");

// 队列管理（可选）
size_t queue_size = audio_queue_size();
bool is_empty = audio_queue_is_empty();
audio_queue_clear(); // 清空队列
```

## 注意事项

1. **内存使用**: 队列会占用一定的内存，但通常语音播放请求不会积累太多。

2. **队列清理**: 在某些情况下（如页面切换），可能需要调用 `audio_queue_clear()` 来清空不相关的语音请求。

3. **错误处理**: 如果队列中的语音文件播放失败，会记录警告日志，但不会影响队列中其他文件的播放。

## 总结

语音播放队列系统成功解决了语音播放冲突的问题，确保所有语音提示都能按顺序播放，提升了用户体验。系统设计简洁、高效，并且保持了良好的向后兼容性。
